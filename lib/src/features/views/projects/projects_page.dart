import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/utils/app_constants.dart';
import 'package:admin_dubai/src/features/bloc/auth_blok.dart';
import 'package:admin_dubai/src/features/bloc/category_bloc.dart';
import 'package:admin_dubai/src/features/response/category_response.dart';
import 'package:admin_dubai/src/features/models/video_model.dart';
import 'package:admin_dubai/src/features/views/projects/add_project/add_project_page.dart';
import 'package:admin_dubai/src/features/views/projects/widgets/project_card.dart';
import 'package:admin_dubai/src/features/views/projects/widgets/delete_project.dart';
import 'package:flutter/material.dart';

import '../../../core/shared_widgets/ad_circular_progress_indicator.dart';
import '../../../core/shared_widgets/snack_bar.dart';
import '../../../core/utils/ad_utils.dart';
import '../../../core/utils/resources.dart';

class ProjectsPage extends StatefulWidget {
  @override
  _ProjectsPageState createState() => _ProjectsPageState();
}

class _ProjectsPageState extends State<ProjectsPage> {
  TextEditingController searchController = TextEditingController();
  bool isLoading = false;

  @override
  void initState() {
    categoryBloc.getCategories(AppConstants.projectsId.toString(), 0, 200);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: GlobalColors.primaryColor,
          centerTitle: true,
          title: Text(S.of(context).Projects),
          actions: [
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AddProjectPage(),
                  ),
                );
              },
            ),
          ],
        ),
        body: Column(
          children: [
            // Search bar
            Container(
              padding: const EdgeInsets.all(16),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(25),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.2),
                      spreadRadius: 1,
                      blurRadius: 5,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: TextFormField(
                  controller: searchController,
                  textInputAction: TextInputAction.search,
                  onFieldSubmitted: (value) {
                    categoryBloc.getCategories(
                        AppConstants.projectsId.toString(),
                        0,
                        200,
                        value);
                  },
                  decoration: InputDecoration(
                    prefixIcon: const Icon(
                      Icons.search,
                      color: Color(0xff8B959E),
                    ),
                    contentPadding: const EdgeInsets.only(
                        left: 20, right: 20, top: 5),
                    hintText: S.of(context).SearchProjects,
                    hintStyle: const TextStyle(
                        color: Color(0xff8B959E),
                        fontSize: 13),
                    border: InputBorder.none,
                  ),
                ),
              ),
            ),
            // Projects list
            Expanded(
              child: StreamBuilder<CategoryResponse?>(
                stream: categoryBloc.subject.stream,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: ADCircularProgressIndicator());
                  }

                  if (snapshot.hasError) {
                    return Center(
                      child: Text(S.of(context).ErrorLoadingProjects),
                    );
                  }

                  if (!snapshot.hasData || snapshot.data!.category.isEmpty) {
                    return Center(
                      child: Text(S.of(context).NoProjectsFound),
                    );
                  }

                  final projects = snapshot.data!.category.map((categoryModel) {
                    return VideoModel(
                      id: categoryModel.id,
                      name: categoryModel.name,
                      description: categoryModel.description,
                      images: categoryModel.image,
                      video: AuthBloc.isEnglish == true
                          ? categoryModel.video
                          : categoryModel.videoAr,
                      locationName: categoryModel.locationName,
                      paymentMethod: 'cash', // Default or from API
                      latitude: categoryModel.lat,
                      longitude: categoryModel.lng,
                      featuredHome: categoryModel.featuredHome,
                      featuredCategory: categoryModel.featuredCategory,
                      createdAt: categoryModel.createdAt,
                      updatedAt: categoryModel.updatedAt,
                      // Add project plans if available from API
                      projectPlans: [], // Will be populated from API response
                    );
                  }).toList();

                  return ListView.builder(
                    padding: const EdgeInsets.all(8),
                    itemCount: projects.length,
                    itemBuilder: (context, index) {
                      final project = projects[index];
                      return ProjectCard(
                        project: project,
                        onEdit: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => AddProjectPage(
                                project: project,
                              ),
                            ),
                          );
                        },
                        onDelete: () {
                          deleteProject(
                            project.id,
                            index,
                            context: context,
                            isLoading: isLoading,
                          );
                        },
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
