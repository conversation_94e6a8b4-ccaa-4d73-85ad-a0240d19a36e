import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/features/models/category_model.dart';
import 'package:flutter/materialre/utils/resources.dart';

import '../../../bloc/auth_blok.dart';
import '../../video_details/video_details.dart';

class ProjectCard extends StatelessWidget {
  final CategoryModel project;
  final String? date;
  final String? time;
  final int? planId;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const ProjectCard({
    Key? key,
    required this.project,
    this.date,
    this.time,
    this.planId,
    this.onEdit,
    this.onDelete,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Use CategoryModel properties directly

    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(MaterialPageRoute(
          builder: (BuildContext context) => ChewieDemo(
            id: project.id,
            image: project.image,
            video: AuthBloc.isEnglish == true ? project.video : project.videoAr,
            categoryName: "Projects",
            withDetails: true,
          ),
        ));
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              spreadRadius: 1,
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Image section (half of card height)
                ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                  child: SizedBox(
                    height: 150,
                    width: double.infinity,
                    child: project.image?.isNotEmpty == true
                        ? Image.network(
                            project.image!,
                            fit: BoxFit.cover,
                          )
                        : Container(
                            color: Colors.grey[300],
                            child: const Icon(
                              Icons.image,
                              size: 50,
                              color: Colors.grey,
                            ),
                          ),
                  ),
                ),
                // Content section
                Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Project name
                      const SizedBox(height: 8),
                      Text(
                        project.name ?? '',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 8),

                      // Location
                      Row(
                        children: [
                          Icon(
                            Icons.location_on,
                            size: 16,
                            color: GlobalColors.primaryColor,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              project.locationName ?? '',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),

                      // Price
                      Row(
                        children: [
                          Icon(
                            Icons.attach_money,
                            size: 16,
                            color: GlobalColors.primaryColor,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              project.startPrice != null
                                  ? '${S.of(context).PriceFrom} ${project.startPrice} AED'
                                  : project.price != null
                                      ? '${project.price} AED'
                                      : '-',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 8),

                      // Bedrooms
                      Row(
                        children: [
                          Icon(
                            Icons.bed,
                            size: 16,
                            color: GlobalColors.primaryColor,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              project.rooms?.isNotEmpty == true
                                  ? '${project.rooms} ${S.of(context).Bedrooms}'
                                  : project.size != null
                                      ? '${project.size} sqft'
                                      : '-',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            // Edit and Delete buttons
            if (onEdit != null || onDelete != null)
              Positioned(
                top: 10,
                right: 10,
                child: Row(
                  children: [
                    if (onEdit != null)
                      GestureDetector(
                        onTap: onEdit,
                        child: Container(
                          height: 30,
                          width: 30,
                          margin: const EdgeInsets.only(right: 5),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(25),
                            color: const Color(0xff233549).withOpacity(0.7),
                          ),
                          child: const Center(
                            child: Icon(
                              Icons.edit,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),
                      ),
                    if (onDelete != null)
                      GestureDetector(
                        onTap: onDelete,
                        child: Container(
                          height: 30,
                          width: 30,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(25),
                            color: Colors.red.withOpacity(0.7),
                          ),
                          child: const Center(
                            child: Icon(
                              Icons.delete,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}
