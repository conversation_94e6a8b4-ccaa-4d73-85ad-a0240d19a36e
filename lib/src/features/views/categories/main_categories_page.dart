import 'package:admin_dubai/generated/l10n.dart';
import 'package:admin_dubai/src/core/shared_widgets/ad_circular_progress_indicator.dart';
import 'package:admin_dubai/src/core/utils/resources.dart';
import 'package:admin_dubai/src/features/bloc/category_bloc.dart';
import 'package:admin_dubai/src/features/models/main_category_model.dart';
import 'package:admin_dubai/src/features/views/projects/add_project/add_project_page.dart';
import 'package:admin_dubai/src/features/views/projects/projects_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../properties/properties/properties_page.dart';

class Categories extends StatefulWidget {
  const Categories({super.key});

  @override
  _Categories createState() => _Categories();
}

class _Categories extends State<Categories> {
  @override
  void initState() {
    super.initState();
    categoryBloc.getMainCategories();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: GlobalColors.primaryColor,
          centerTitle: true,
          title: Text(S.of(context).Categories),
        ),
        body: Container(
          padding: const EdgeInsets.only(top: 20),
          child: StreamBuilder<List<MainCategoryModel>?>(
              stream: categoryBloc.mainCategoriesSubject.stream,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: ADCircularProgressIndicator());
                }
                final category = snapshot.data ?? [];
                return Column(
                  children: [
                    for (var i = 0; i < category.length; i++)
                      Container(
                        padding: const EdgeInsets.only(right: 5, left: 5),
                        child: GestureDetector(
                          onTap: () {
                            if (category[i].id == 8) {
                              Navigator.push(context,
                                  MaterialPageRoute(builder: (context) {
                                return Properties();
                              }));
                            } else {
                              Navigator.push(context,
                                  MaterialPageRoute(builder: (context) {
                                return const AddProjectPage();
                              }));
                            }
                          },
                          child: Container(
                            decoration: BoxDecoration(
                                color: GlobalColors.primaryColor,
                                borderRadius: BorderRadius.circular(15)),
                            height: 130,
                            width: MediaQuery.of(context).size.width,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 20,
                            ),
                            margin: const EdgeInsets.only(bottom: 12),
                            child: Row(
                              children: [
                                Container(
                                  height: 80,
                                  width: 80,
                                  decoration: BoxDecoration(
                                      color: GlobalColors.secondaryColor,
                                      borderRadius: BorderRadius.circular(120)),
                                  child: Center(
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(5),
                                      child: SizedBox(
                                        width: 50,
                                        height: 23,
                                        child: category[i]
                                                    .icon
                                                    ?.contains('svg') ==
                                                true
                                            ? SvgPicture.network(
                                                category[i].icon!,
                                                // semanticsLabel: 'Acme Logo',
                                                color:
                                                    GlobalColors.primaryColor,
                                                // fit: BoxFit.cover,
                                              )
                                            : Image.network(
                                                category[i].icon!,
                                                // semanticsLabel: 'Acme Logo',
                                                color:
                                                    GlobalColors.primaryColor,
                                                // fit: BoxFit.cover,
                                              ),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(
                                  width: 15,
                                ),
                                Center(
                                  child: Builder(builder: (context) {
                                    return Text(
                                      category[i].name ?? '',
                                      style: const TextStyle(
                                          color: Colors.white, fontSize: 16),
                                    );
                                  }
                                      //   if (category[i].id == 3) {
                                      //     return Text(
                                      //       S.of(context).Activities,
                                      //       style: const TextStyle(
                                      //           color: Colors.white,
                                      //           fontSize: 16),
                                      //     );
                                      //   }
                                      //
                                      //   if (category[i].id == 10) {
                                      //     return Text(
                                      //       S.of(context).NewProjects,
                                      //       style: const TextStyle(
                                      //           color: Colors.white,
                                      //           fontSize: 16),
                                      //     );
                                      //   } else {
                                      //     return Text(
                                      //       S.of(context).Properties,
                                      //       style: const TextStyle(
                                      //           color: Colors.white,
                                      //           fontWeight: FontWeight.bold,
                                      //           fontSize: 16),
                                      //     );
                                      //   }
                                      // },
                                      ),
                                ),
                                const Spacer(),
                                const CircleAvatar(
                                  backgroundColor: GlobalColors.secondaryColor,
                                  radius: 20,
                                  child: Icon(
                                    Icons.arrow_forward_ios,
                                    color: GlobalColors.primaryColor,
                                    size: 18,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                  ],
                );
              }),
        ),
      ),
    );
  }
}
