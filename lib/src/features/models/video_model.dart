import 'package:admin_dubai/src/features/controllers/language_controller.dart';

class ProjectPlanModel {
  String? bedrooms;
  String? priceFrom;
  String? priceTo;
  String? spaceSize;

  ProjectPlanModel({
    this.bedrooms,
    this.priceFrom,
    this.priceTo,
    this.spaceSize,
  });

  ProjectPlanModel.fromJson(Map<String, dynamic> json) {
    bedrooms = json['bedrooms'] ?? '';
    priceFrom = json['price_from']?.toString() ?? '';
    priceTo = json['price_to']?.toString() ?? '';
    spaceSize = json['space_size'] ?? '';
  }
}

class MediaModel {
  int? id;
  String? url;

  MediaModel({this.id, this.url});

  MediaModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    url = json['url'];
  }
}

class FloorPlanModel {
  int? id;
  int? videoId;
  String? name;
  List<MediaModel>? media;

  FloorPlanModel({
    this.id,
    this.videoId,
    this.name,
    this.media,
  });

  FloorPlanModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    videoId = json['video_id'];
    name = json['name'];
    media = json['media'] != null
        ? List<MediaModel>.from(
            json['media'].map((x) => MediaModel.fromJson(x)))
        : [];
  }
}

class ReraPermitModel {
  int? id;
  int? videoId;
  String? reraNumber;
  List<MediaModel>? media;

  ReraPermitModel({
    this.id,
    this.videoId,
    this.reraNumber,
    this.media,
  });

  ReraPermitModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    videoId = json['video_id'];
    reraNumber = json['rera_number'];
    media = json['media'] != null
        ? List<MediaModel>.from(
            json['media'].map((x) => MediaModel.fromJson(x)))
        : [];
  }
}

class PropertyStatusModel {
  int? id;
  String? name;

  PropertyStatusModel({
    this.id,
    this.name,
  });

  PropertyStatusModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }
}

class DeveloperModel {
  int? id;
  String? name;
  String? description;
  String? developerLogo;

  DeveloperModel({
    this.id,
    this.name,
    this.description,
    this.developerLogo,
  });

  DeveloperModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    developerLogo = json['developer_logo'];
  }
}

class VideoModel {
  int? id;
  String? name;
  String? description;
  String? images;
  String? video;
  String? locationName;
  String? paymentMethod;
  List<ProjectPlanModel>? projectPlans;
  List<FloorPlanModel>? floorPlans;
  List<ReraPermitModel>? reraPermits;
  PropertyStatusModel? propertyStatus;
  DeveloperModel? developer;
  double? latitude;
  double? longitude;
  int? featuredHome;
  int? featuredCategory;
  String? createdAt;
  String? updatedAt;

  VideoModel({
    this.id,
    this.name,
    this.description,
    this.images,
    this.video,
    this.locationName,
    this.paymentMethod,
    this.projectPlans,
    this.floorPlans,
    this.reraPermits,
    this.propertyStatus,
    this.developer,
    this.latitude,
    this.longitude,
    this.featuredHome,
    this.featuredCategory,
    this.createdAt,
    this.updatedAt,
  });

  VideoModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    images = json['images'] != null && json['images'].isNotEmpty
        ? json['images'][0]['url']
        : null;
    video = json['video'];
    locationName = json['location_name'];
    paymentMethod = json['payment_method'];
    latitude = json['latitude']?.toDouble();
    longitude = json['longitude']?.toDouble();
    featuredHome = json['featuredHome'];
    featuredCategory = json['featuredCategory'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];

    // Parse project plans
    if (json['project_plans'] != null) {
      projectPlans = <ProjectPlanModel>[];
      json['project_plans'].forEach((v) {
        projectPlans!.add(ProjectPlanModel.fromJson(v));
      });
    }

    // Parse floor plans
    if (json['floor_plans'] != null) {
      floorPlans = <FloorPlanModel>[];
      json['floor_plans'].forEach((v) {
        floorPlans!.add(FloorPlanModel.fromJson(v));
      });
    }

    // Parse rera permits
    if (json['rera_permits'] != null) {
      reraPermits = <ReraPermitModel>[];
      json['rera_permits'].forEach((v) {
        reraPermits!.add(ReraPermitModel.fromJson(v));
      });
    }

    // Parse property status
    if (json['property_status'] != null) {
      propertyStatus = PropertyStatusModel.fromJson(json['property_status']);
    }

    // Parse developer
    if (json['developer'] != null) {
      developer = DeveloperModel.fromJson(json['developer']);
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['description'] = description;
    data['images'] = images;
    data['video'] = video;
    data['location_name'] = locationName;
    data['payment_method'] = paymentMethod;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['featuredHome'] = featuredHome;
    data['featuredCategory'] = featuredCategory;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;

    if (projectPlans != null) {
      data['project_plans'] = projectPlans!.map((v) => v.toJson()).toList();
    }
    if (floorPlans != null) {
      data['floor_plans'] = floorPlans!.map((v) => v.toJson()).toList();
    }
    if (reraPermits != null) {
      data['rera_permits'] = reraPermits!.map((v) => v.toJson()).toList();
    }
    if (propertyStatus != null) {
      data['property_status'] = propertyStatus!.toJson();
    }
    if (developer != null) {
      data['developer'] = developer!.toJson();
    }
    return data;
  }
}
